import { waitFor } from '@testing-library/react'
import * as routerModule from 'next/router'

import { getProxyEndpoint } from '@core/constants/api.constants'
import { addAlert } from '@core/redux/features/alerts/AlertSlice'
import { useMarket } from '@core/redux/features/market'
import { AlertType } from '@core/redux/types'
import { http } from '@core/services/http/http-request'
import {
  createReactQueryWrapper,
  renderHookWithRedux
} from '@core/utils/testing'

import { useChatList } from '../useChatlist'
import { useOrder } from '../useOrder'

// Mock all dependencies
jest.mock('@core/services/http/http-request')
jest.mock('@core/constants/api.constants')
jest.mock('@core/redux/features/market')
jest.mock('@core/redux/features/alerts/AlertSlice')
jest.mock('../useOrder')
jest.mock('next/config', () => () => ({
  publicRuntimeConfig: {
    appApiBaseUrl: 'http://mock-api-url',
    apiSchema: 'https',
    apiDomain: 'mock-domain.com',
    cdnBaseUrlDomain: 'cdn',
    webAppBuyerUrl: 'http://mock-buyer-url'
  }
}))
jest.mock('@core/utils/getBaseHref', () => ({
  getBaseHref: jest.fn(() => 'mock-base')
}))
jest.mock('@metromarkets/message-center-sdk', () => ({
  Order: undefined,
  ChatBadgeStatus: { NEW: 'NEW' }
}))
jest.mock('@modules/message-center/hooks/useIsSellerConversation', () => ({
  useIsSellerConversation: () => jest.fn(() => true)
}))
jest.mock('@core/hooks', () => ({
  useGtag: jest.fn(() => ({
    gtagEvent: jest.fn(),
    gtag: jest.fn()
  }))
}))
jest.mock(
  '@modules/message-center/components/MessageCenter/useMessageCenterEvents',
  () => ({
    useMessageCenterEvents: jest.fn(() => ({
      chatSearchBarUsedEvent: jest.fn()
    }))
  })
)

describe('useChatList with React Query', () => {
  const mockMarket = 'DE'
  const mockUrl = 'mock-url'
  let pushMock: jest.Mock

  const renderUseChatListHook = (orderId: string, organizationId?: string) => {
    const { wrapper, queryClient } = createReactQueryWrapper()
    return {
      ...renderHookWithRedux(
        () => useChatList({ orderId, organizationId }),
        {},
        wrapper
      ),
      queryClient
    }
  }

  beforeEach(() => {
    jest.clearAllMocks()
    pushMock = jest.fn()
    ;(useMarket as jest.Mock).mockReturnValue(mockMarket)
    ;(getProxyEndpoint as jest.Mock).mockReturnValue(mockUrl)
    ;(addAlert as jest.Mock).mockReturnValue({
      type: 'alerts/addAlert',
      payload: {}
    })
    ;(useOrder as jest.Mock).mockReturnValue({
      order: { id: 'order123', orderNumber: '123' },
      isLoading: false,
      error: null
    })
    jest.spyOn(routerModule, 'useRouter').mockReturnValue({
      query: { orderId: undefined },
      push: pushMock
    } as any)
  })

  it('should use React Query wrapper and renderHookWithRedux', () => {
    ;(http as jest.Mock).mockResolvedValue({ data: { data: [] } })

    const { result, queryClient } = renderUseChatListHook('order1', 'org1')

    // Verify that the hook is properly wrapped with React Query and Redux
    expect(result.current).toBeDefined()
    expect(queryClient).toBeDefined()
    expect(typeof result.current.fetchChats).toBe('function')
    expect(typeof result.current.selectChat).toBe('function')
    expect(typeof result.current.onSearch).toBe('function')
  })

  it('should demonstrate React Query and Redux integration', async () => {
    const mockChats = [
      {
        id: '1',
        order: { id: 'order1', orderNumber: '123' },
        isRead: false,
        isSelected: true,
        seller: { organization: { id: 'org1' } }
      }
    ]
    ;(http as jest.Mock).mockResolvedValue({ data: { data: mockChats } })

    const { result } = renderUseChatListHook('order1', 'org1')

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    // Verify that the hook uses React Query for data fetching
    expect(getProxyEndpoint).toHaveBeenCalledWith(
      mockMarket,
      'message-center/get-chats'
    )
    expect(http).toHaveBeenCalledWith(
      mockUrl,
      { method: 'get' },
      { market: mockMarket }
    )

    // Verify that data is properly loaded
    expect(result.current.filteredChats).toHaveLength(1)
    expect(result.current.filteredChats[0].id).toBe('1')
  })

  it('should handle errors using React Query pattern', async () => {
    ;(http as jest.Mock).mockRejectedValue(
      new Error('Error fetching chat list')
    )

    const { result } = renderUseChatListHook('order1', 'org1')

    await waitFor(() => {
      expect(result.current.error).toBeTruthy()
    })

    // Verify that errors are handled through Redux alerts
    expect(addAlert).toHaveBeenCalledWith({
      alertKey: 'MC_ERROR',
      forAllPages: true,
      isRepeatable: true,
      type: AlertType.ERROR,
      message: 'Error fetching chat list'
    })
  })
})
